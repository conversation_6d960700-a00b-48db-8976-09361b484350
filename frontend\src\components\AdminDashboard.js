import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Container
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  School,
  People,
  WorkspacePremium,
  Person,
  Help,
  Chat,
  Analytics,
  Settings,
  Logout,
  Notifications,
  VideoLibrary,
  Group,
  BarChart,
  AccountCircle,
  Add
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { seedAdminData } from '../utils/seedAdminData';
import toast from 'react-hot-toast';

// Import components for different sections
import DashboardOverview from './admin/DashboardOverview';
import CourseManagement from './admin/CourseManagement';
import StudentManagement from './admin/StudentManagement';
import StudentCodeManagement from './admin/StudentCodeManagement';

import CourseVideoManagement from './admin/CourseVideoManagement';
import StudentEnrollmentManagement from './admin/StudentEnrollmentManagement';
import RealtimeIndicator from './admin/RealtimeIndicator';
import NotificationPanel from './admin/NotificationPanel';
import CertificateManagement from './admin/CertificateManagement';
import AdminProfile from './admin/AdminProfile';
import FAQManagement from './admin/FAQManagement';
import ChatSystem from './admin/ChatSystem';
import AnalyticsReports from './admin/AnalyticsReports';
import PasswordResetTest from './admin/PasswordResetTest';

const drawerWidth = 280;

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [mobileOpen, setMobileOpen] = useState(false);
  const [selectedSection, setSelectedSection] = useState('dashboard');
  const [anchorEl, setAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);

  const menuItems = [
    { id: 'dashboard', label: 'لوحة التحكم', icon: <Dashboard />, color: '#2196F3' },
    { id: 'courses', label: 'إدارة الكورسات', icon: <VideoLibrary />, color: '#4CAF50' },
    { id: 'course-videos', label: 'الكورسات والفيديوهات', icon: <VideoLibrary />, color: '#4CAF50' },
    { id: 'students', label: 'إدارة الطلاب', icon: <Group />, color: '#FF9800' },
    { id: 'student-codes', label: 'أكواد التسجيل', icon: <Add />, color: '#FF9800' },
    { id: 'enrollments', label: 'تسجيل الطلاب', icon: <School />, color: '#FF9800' },
    { id: 'certificates', label: 'الشهادات', icon: <WorkspacePremium />, color: '#9C27B0' },
    { id: 'faq', label: 'الأسئلة الشائعة', icon: <Help />, color: '#00BCD4' },
    { id: 'chat', label: 'التواصل', icon: <Chat />, color: '#E91E63' },
    { id: 'analytics', label: 'التقارير والإحصائيات', icon: <BarChart />, color: '#795548' },
    { id: 'password-test', label: 'اختبار استرداد كلمة المرور', icon: <Security />, color: '#F44336' },
    { id: 'notifications', label: 'الإشعارات', icon: <Notifications />, color: '#FF5722' },
    { id: 'profile', label: 'الملف الشخصي', icon: <Person />, color: '#607D8B' }
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error) {
      toast.error('خطأ في تسجيل الخروج');
    }
    handleMenuClose();
  };

  const handleSeedData = async () => {
    if (window.confirm('هل تريد إنشاء البيانات الأولية؟ سيتم إضافة بيانات تجريبية للطلاب والكورسات والأسئلة الشائعة.')) {
      try {
        toast.loading('جاري إنشاء البيانات الأولية...');
        await seedAdminData();
        toast.dismiss();
        toast.success('تم إنشاء البيانات الأولية بنجاح!');
        // إعادة تحميل الصفحة لعرض البيانات الجديدة
        window.location.reload();
      } catch (error) {
        toast.dismiss();
        toast.error('فشل في إنشاء البيانات الأولية');
        console.error('Error seeding data:', error);
      }
    }
  };

  const renderContent = () => {
    switch (selectedSection) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'courses':
        return <CourseManagement />;
      case 'course-videos':
        return <CourseVideoManagement />;
      case 'students':
        return <StudentManagement />;
      case 'student-codes':
        return <StudentCodeManagement />;
      case 'enrollments':
        return <StudentEnrollmentManagement />;
      case 'certificates':
        return <CertificateManagement />;
      case 'faq':
        return <FAQManagement />;
      case 'chat':
        return <ChatSystem />;
      case 'analytics':
        return <AnalyticsReports />;
      case 'password-test':
        return <PasswordResetTest />;
      case 'notifications':
        return <NotificationPanel />;
      case 'profile':
        return <AdminProfile />;
      default:
        return <DashboardOverview />;
    }
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          color: 'white',
          textAlign: 'center'
        }}
      >
        <School sx={{ fontSize: '2.5rem', mb: 1, color: '#FFD700' }} />
        <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
          SKILLS WORLD ACADEMY
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
          لوحة تحكم المدير
        </Typography>
      </Box>

      <Divider />

      {/* Menu Items */}
      <List sx={{ flexGrow: 1, px: 1, py: 2 }}>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              selected={selectedSection === item.id}
              onClick={() => {
                setSelectedSection(item.id);
                if (isMobile) setMobileOpen(false);
              }}
              sx={{
                borderRadius: 2,
                mx: 1,
                '&.Mui-selected': {
                  backgroundColor: `${item.color}15`,
                  borderLeft: `4px solid ${item.color}`,
                  '& .MuiListItemIcon-root': {
                    color: item.color
                  },
                  '& .MuiListItemText-primary': {
                    color: item.color,
                    fontWeight: 'bold'
                  }
                },
                '&:hover': {
                  backgroundColor: `${item.color}08`
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                primaryTypographyProps={{
                  fontSize: '0.95rem'
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      {/* Footer */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          © 2024 Skills World Academy
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: '#f5f5f5', direction: 'rtl' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
          mr: { md: `${drawerWidth}px` },
          ml: { xs: 0, md: 0 },
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          boxShadow: '0 4px 20px rgba(0,0,255,0.3)',
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => item.id === selectedSection)?.label || 'لوحة التحكم'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* مؤشر التحديثات الفورية */}
            <RealtimeIndicator />

            <Tooltip title="الإشعارات">
              <IconButton color="inherit">
                <Badge badgeContent={notifications.length} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="الملف الشخصي">
              <IconButton onClick={handleMenuClick} color="inherit">
                <Avatar
                  sx={{ width: 32, height: 32, bgcolor: '#FFD700', color: '#0000FF' }}
                >
                  {user?.name?.charAt(0) || 'A'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { xs: '100%', md: `calc(100% - ${drawerWidth}px - 40px)` },
          minHeight: '100vh',
          direction: 'ltr',
          ml: { xs: 0, md: 0 },
          mr: { xs: 0, md: `${drawerWidth + 40}px` },
          pr: { xs: 0, md: 3 }
        }}
      >
        <Toolbar />
        <Container
          maxWidth={false}
          sx={{
            py: 3,
            px: { xs: 2, md: 4 },
            width: '100%',
            maxWidth: { xs: '100%', md: `calc(100vw - ${drawerWidth}px - 80px)` },
            ml: 0,
            mr: { xs: 0, md: `${drawerWidth + 40}px` }
          }}
        >
          {renderContent()}
        </Container>
      </Box>

      {/* Navigation Drawer */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              position: 'fixed',
              height: '100%',
              top: 0,
              right: 0,
              zIndex: (theme) => theme.zIndex.drawer
            }
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>



      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { setSelectedSection('profile'); handleMenuClose(); }}>
          <ListItemIcon><AccountCircle /></ListItemIcon>
          الملف الشخصي
        </MenuItem>
        <MenuItem onClick={() => { setSelectedSection('settings'); handleMenuClose(); }}>
          <ListItemIcon><Settings /></ListItemIcon>
          الإعدادات
        </MenuItem>
        <MenuItem onClick={handleSeedData}>
          <ListItemIcon><Add /></ListItemIcon>
          إنشاء بيانات أولية
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon><Logout /></ListItemIcon>
          تسجيل الخروج
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default AdminDashboard;
