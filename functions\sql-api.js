const functions = require('firebase-functions');
const mysql = require('mysql2/promise');
const cors = require('cors')({ origin: true });

// إعداد قاعدة البيانات
const dbConfig = {
  host: functions.config().db?.host || process.env.DB_HOST,
  user: functions.config().db?.user || process.env.DB_USER,
  password: functions.config().db?.password || process.env.DB_PASSWORD,
  database: functions.config().db?.name || process.env.DB_NAME,
  port: functions.config().db?.port || process.env.DB_PORT || 3306,
  ssl: {
    rejectUnauthorized: false
  },
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

/**
 * إنشاء اتصال بقاعدة البيانات
 */
const createConnection = async () => {
  try {
    const connection = await mysql.createConnection(dbConfig);
    return connection;
  } catch (error) {
    console.error('Database connection error:', error);
    throw new Error('فشل في الاتصال بقاعدة البيانات');
  }
};

/**
 * تنفيذ استعلام SQL
 */
exports.executeSQL = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    if (req.method !== 'POST') {
      return res.status(405).json({ success: false, message: 'Method not allowed' });
    }

    const { query, params = [] } = req.body;

    if (!query) {
      return res.status(400).json({ success: false, message: 'Query is required' });
    }

    let connection;
    try {
      connection = await createConnection();
      const [rows, fields] = await connection.execute(query, params);
      
      res.json({
        success: true,
        data: rows,
        fields: fields,
        insertId: rows.insertId,
        affectedRows: rows.affectedRows
      });
    } catch (error) {
      console.error('SQL execution error:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  });
});

/**
 * API للطلاب
 */
exports.studentsAPI = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    let connection;
    try {
      connection = await createConnection();

      switch (req.method) {
        case 'GET':
          // جلب جميع الطلاب
          const [students] = await connection.execute(`
            SELECT 
              u.*,
              COUNT(DISTINCT e.id) as enrolled_courses,
              COUNT(DISTINCT c.id) as completed_courses,
              COALESCE(SUM(vp.watch_time_seconds), 0) / 60 as total_watch_minutes
            FROM users u
            LEFT JOIN enrollments e ON u.id = e.student_id
            LEFT JOIN certificates c ON u.id = c.student_id
            LEFT JOIN video_progress vp ON u.id = vp.student_id
            WHERE u.role = 'student'
            GROUP BY u.id
            ORDER BY u.created_at DESC
          `);

          res.json({ success: true, students });
          break;

        case 'POST':
          // إنشاء طالب جديد
          const { name, email, phone } = req.body;
          const studentCode = Math.floor(100000 + Math.random() * 900000).toString();

          const [result] = await connection.execute(`
            INSERT INTO users (name, email, phone, role, student_code, is_active, created_at, updated_at)
            VALUES (?, ?, ?, 'student', ?, TRUE, NOW(), NOW())
          `, [name, email || null, phone || null, studentCode]);

          res.json({
            success: true,
            student: {
              id: result.insertId,
              name,
              email,
              phone,
              studentCode,
              role: 'student',
              isActive: true,
              createdAt: new Date()
            }
          });
          break;

        case 'PUT':
          // تحديث طالب
          const { studentId, updateData } = req.body;
          const fields = [];
          const params = [];

          Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined) {
              fields.push(`${key} = ?`);
              params.push(updateData[key]);
            }
          });

          fields.push('updated_at = NOW()');
          params.push(studentId);

          await connection.execute(`
            UPDATE users 
            SET ${fields.join(', ')}
            WHERE id = ? AND role = 'student'
          `, params);

          res.json({ success: true, message: 'تم تحديث الطالب بنجاح' });
          break;

        case 'DELETE':
          // حذف طالب
          const { id } = req.query;
          await connection.execute('DELETE FROM users WHERE id = ? AND role = "student"', [id]);
          res.json({ success: true, message: 'تم حذف الطالب بنجاح' });
          break;

        default:
          res.status(405).json({ success: false, message: 'Method not allowed' });
      }
    } catch (error) {
      console.error('Students API error:', error);
      res.status(500).json({ success: false, message: error.message });
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  });
});

/**
 * API للمصادقة
 */
exports.authAPI = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    if (req.method !== 'POST') {
      return res.status(405).json({ success: false, message: 'Method not allowed' });
    }

    const { type, email, password, studentCode } = req.body;
    let connection;

    try {
      connection = await createConnection();

      if (type === 'admin') {
        // تسجيل دخول المدير
        if (email === '<EMAIL>' && password === 'Admin123!') {
          const [admins] = await connection.execute(
            'SELECT * FROM users WHERE email = ? AND role = "admin" LIMIT 1',
            [email]
          );

          if (admins.length > 0) {
            const admin = admins[0];
            
            // تحديث آخر تسجيل دخول
            await connection.execute(
              'UPDATE users SET last_login = NOW() WHERE id = ?',
              [admin.id]
            );

            res.json({
              success: true,
              user: {
                id: admin.id,
                name: admin.name,
                email: admin.email,
                phone: admin.phone,
                role: admin.role,
                isActive: admin.is_active
              }
            });
          } else {
            res.status(401).json({ success: false, message: 'المدير غير موجود' });
          }
        } else {
          res.status(401).json({ success: false, message: 'بيانات تسجيل الدخول غير صحيحة' });
        }
      } else if (type === 'student') {
        // تسجيل دخول الطالب
        const [students] = await connection.execute(
          'SELECT * FROM users WHERE student_code = ? AND role = "student" LIMIT 1',
          [studentCode]
        );

        if (students.length > 0) {
          const student = students[0];

          if (!student.is_active) {
            return res.status(401).json({ success: false, message: 'حساب الطالب غير مفعل' });
          }

          // تحديث آخر تسجيل دخول
          await connection.execute(
            'UPDATE users SET last_login = NOW() WHERE id = ?',
            [student.id]
          );

          res.json({
            success: true,
            user: {
              id: student.id,
              name: student.name,
              email: student.email,
              phone: student.phone,
              role: student.role,
              studentCode: student.student_code,
              isActive: student.is_active
            }
          });
        } else {
          res.status(401).json({ success: false, message: 'كود التسجيل غير صحيح' });
        }
      } else {
        res.status(400).json({ success: false, message: 'نوع تسجيل الدخول غير صحيح' });
      }
    } catch (error) {
      console.error('Auth API error:', error);
      res.status(500).json({ success: false, message: error.message });
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  });
});

/**
 * API للكورسات
 */
exports.coursesAPI = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    let connection;
    try {
      connection = await createConnection();

      switch (req.method) {
        case 'GET':
          // جلب جميع الكورسات
          const [courses] = await connection.execute(`
            SELECT 
              c.*,
              COUNT(DISTINCT e.student_id) as enrolled_students,
              COUNT(DISTINCT cv.id) as total_videos
            FROM courses c
            LEFT JOIN enrollments e ON c.id = e.course_id
            LEFT JOIN course_videos cv ON c.id = cv.course_id
            GROUP BY c.id
            ORDER BY c.created_at DESC
          `);

          res.json({ success: true, courses });
          break;

        case 'POST':
          // إنشاء كورس جديد
          const { title, description, instructor, duration, level, price } = req.body;

          const [result] = await connection.execute(`
            INSERT INTO courses (title, description, instructor, duration, level, price, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
          `, [title, description, instructor, duration, level || 'مبتدئ', price || 0]);

          res.json({
            success: true,
            course: {
              id: result.insertId,
              title,
              description,
              instructor,
              duration,
              level,
              price,
              isActive: true,
              enrolledStudents: 0,
              totalVideos: 0,
              createdAt: new Date()
            }
          });
          break;

        default:
          res.status(405).json({ success: false, message: 'Method not allowed' });
      }
    } catch (error) {
      console.error('Courses API error:', error);
      res.status(500).json({ success: false, message: error.message });
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  });
});

/**
 * API للإحصائيات
 */
exports.statsAPI = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    if (req.method !== 'GET') {
      return res.status(405).json({ success: false, message: 'Method not allowed' });
    }

    let connection;
    try {
      connection = await createConnection();

      // إحصائيات عامة
      const [totalStudents] = await connection.execute('SELECT COUNT(*) as total FROM users WHERE role = "student"');
      const [activeStudents] = await connection.execute('SELECT COUNT(*) as total FROM users WHERE role = "student" AND is_active = TRUE');
      const [totalCourses] = await connection.execute('SELECT COUNT(*) as total FROM courses WHERE is_active = TRUE');
      const [totalEnrollments] = await connection.execute('SELECT COUNT(*) as total FROM enrollments');
      const [totalCertificates] = await connection.execute('SELECT COUNT(*) as total FROM certificates');

      res.json({
        success: true,
        stats: {
          totalStudents: totalStudents[0].total,
          activeStudents: activeStudents[0].total,
          totalCourses: totalCourses[0].total,
          totalEnrollments: totalEnrollments[0].total,
          totalCertificates: totalCertificates[0].total
        }
      });
    } catch (error) {
      console.error('Stats API error:', error);
      res.status(500).json({ success: false, message: error.message });
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  });
});
