import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Avatar,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  ContentCopy,
  Block,
  CheckCircle
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { createStudent, getAllStudents, updateStudent, deleteStudent, toggleStudentStatus } from '../../firebase/studentService';
import { useAuth } from '../../contexts/AuthContext';

const StudentManagement = () => {
  const { user } = useAuth();
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    isActive: true
  });

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const studentsData = await getAllStudents();
      setStudents(studentsData);
    } catch (error) {
      console.error('خطأ في جلب الطلاب:', error);
      toast.error('فشل في تحميل بيانات الطلاب');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (student = null) => {
    if (student) {
      setEditingStudent(student);
      setFormData({
        name: student.name,
        email: student.email || '',
        phone: student.phone || '',
        isActive: student.isActive
      });
    } else {
      setEditingStudent(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      isActive: true
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      if (editingStudent) {
        // تحديث طالب موجود
        await updateStudent(editingStudent.id, formData, user.id);
        await fetchStudents(); // إعادة جلب البيانات
        toast.success('تم تحديث بيانات الطالب بنجاح');
      } else {
        // إضافة طالب جديد
        const newStudent = await createStudent(formData, user.id);
        await fetchStudents(); // إعادة جلب البيانات
        toast.success(`تم إنشاء الطالب بنجاح! كود الطالب: ${newStudent.studentCode}`);

        // نسخ كود الطالب إلى الحافظة
        if (navigator.clipboard) {
          try {
            await navigator.clipboard.writeText(newStudent.studentCode);
            toast.success('تم نسخ كود الطالب إلى الحافظة');
          } catch (clipboardError) {
            console.log('فشل في نسخ الكود إلى الحافظة');
          }
        }
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
      toast.error(error.message || 'فشل في حفظ بيانات الطالب');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟ سيتم حذف جميع بياناته وتسجيلاته في الكورسات.')) {
      try {
        setLoading(true);
        await deleteStudent(studentId, user.id);
        await fetchStudents(); // إعادة جلب البيانات
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error(error.message || 'فشل في حذف الطالب');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleToggleStatus = async (studentId, currentStatus) => {
    try {
      setLoading(true);
      await toggleStudentStatus(studentId, !currentStatus, user.id);
      await fetchStudents(); // إعادة جلب البيانات
      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الطالب بنجاح`);
    } catch (error) {
      console.error('خطأ في تغيير حالة الطالب:', error);
      toast.error(error.message || 'فشل في تغيير حالة الطالب');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyCode = async (code) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('تم نسخ كود الطالب');
    } catch (error) {
      toast.error('فشل في نسخ الكود');
    }
  };

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <Box sx={{ pr: { xs: 0, md: 4 }, maxWidth: '100%', overflow: 'hidden' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, pr: { xs: 0, md: 2 } }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الطلاب
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ borderRadius: 2 }}
        >
          إضافة طالب جديد
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4, pr: { xs: 0, md: 2 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e3f2fd' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                {students.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي الطلاب
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e8f5e8' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                {students.filter(s => s.isActive).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                الطلاب النشطون
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#fff3e0' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                {students.reduce((total, student) => total + (student.enrolledCourses?.length || 0), 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي التسجيلات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#f3e5f5' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                {students.filter(s => {
                  const today = new Date().toDateString();
                  const lastLogin = s.lastLogin ? new Date(s.lastLogin).toDateString() : null;
                  return lastLogin === today;
                }).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                نشط اليوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الطلاب */}
      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>كود الطالب</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الدورات المسجلة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>تاريخ الانضمام</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>آخر نشاط</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    جاري تحميل بيانات الطلاب...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : students.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    لا يوجد طلاب مسجلين حتى الآن
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              students.map((student) => (
              <TableRow key={student.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2, bgcolor: '#1976d2' }}>
                      {getInitials(student.name)}
                    </Avatar>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                      {student.name}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', mr: 1 }}>
                      {student.studentCode}
                    </Typography>
                    <Tooltip title="نسخ الكود">
                      <IconButton
                        size="small"
                        onClick={() => handleCopyCode(student.studentCode)}
                        disabled={loading}
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.enrolledCourses?.length || 0} دورة
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.createdAt ? new Date(student.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.lastLogin ? new Date(student.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={student.isActive ? <CheckCircle /> : <Block />}
                    label={student.isActive ? 'نشط' : 'غير نشط'}
                    color={student.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="تعديل">
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(student)}
                        sx={{ color: '#1976d2' }}
                        disabled={loading}
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={student.isActive ? 'إلغاء التفعيل' : 'تفعيل'}>
                      <IconButton
                        size="small"
                        onClick={() => handleToggleStatus(student.id, student.isActive)}
                        sx={{ color: student.isActive ? '#ff9800' : '#4caf50' }}
                        disabled={loading}
                      >
                        {student.isActive ? <Block /> : <CheckCircle />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف">
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(student.id)}
                        sx={{ color: '#d32f2f' }}
                        disabled={loading}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog لإضافة/تعديل الطالب */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingStudent ? 'تعديل الطالب' : 'إضافة طالب جديد'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="اسم الطالب"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              sx={{ mb: 3 }}
              required
            />

            <TextField
              fullWidth
              label="البريد الإلكتروني"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              sx={{ mb: 3 }}
            />

            <TextField
              fullWidth
              label="رقم الهاتف"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              sx={{ mb: 3 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="طالب نشط"
            />
            
            {!editingStudent && (
              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                سيتم إنشاء كود طالب مكون من 6 أرقام تلقائياً
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={loading}>
            إلغاء
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.name.trim()}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'جاري الحفظ...' : (editingStudent ? 'تحديث' : 'إضافة')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagement;
