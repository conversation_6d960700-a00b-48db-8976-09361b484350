import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Fab
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  VideoLibrary,
  People,
  School,
  PlayCircle,
  Save,
  Cancel
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمات قاعدة البيانات الحقيقية
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '../../firebase/config';

const CourseManagement = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: 'علاء عبد الحميد',
    duration: '',
    level: 'مبتدئ',
    price: 0,
    isActive: true,
    totalVideos: 0,
    enrolledStudents: 0,
    videos: []
  });

  // جلب الكورسات من قاعدة البيانات مع التحديث الفوري (بدون فهارس معقدة)
  useEffect(() => {
    console.log('🔄 بدء مراقبة الكورسات...');

    const unsubscribe = onSnapshot(
      collection(db, 'courses'),
      (snapshot) => {
        const coursesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));

        // ترتيب البيانات محلياً بدلاً من في الاستعلام
        coursesData.sort((a, b) => b.createdAt - a.createdAt);

        setCourses(coursesData);
        console.log('✅ تم تحديث الكورسات:', coursesData.length);
      },
      (error) => {
        console.error('❌ خطأ في جلب الكورسات:', error);
        toast.error('فشل في جلب الكورسات');
      }
    );

    return () => {
      console.log('🛑 إيقاف مراقبة الكورسات');
      unsubscribe();
    };
  }, []);

  // إضافة كورس جديد
  const handleAddCourse = async () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const courseData = {
        ...formData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, 'courses'), courseData);
      
      console.log('✅ تم إضافة الكورس:', docRef.id);
      toast.success('تم إضافة الكورس بنجاح');
      
      handleCloseDialog();
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      toast.error('فشل في إضافة الكورس');
    } finally {
      setLoading(false);
    }
  };

  // تحديث كورس موجود
  const handleUpdateCourse = async () => {
    if (!editingCourse || !formData.title.trim() || !formData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const courseRef = doc(db, 'courses', editingCourse.id);
      const updateData = {
        ...formData,
        updatedAt: serverTimestamp()
      };

      await updateDoc(courseRef, updateData);
      
      console.log('✅ تم تحديث الكورس:', editingCourse.id);
      toast.success('تم تحديث الكورس بنجاح');
      
      handleCloseDialog();
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      toast.error('فشل في تحديث الكورس');
    } finally {
      setLoading(false);
    }
  };

  // حذف كورس
  const handleDeleteCourse = async (courseId, courseTitle) => {
    if (!window.confirm(`هل أنت متأكد من حذف الكورس "${courseTitle}"؟`)) {
      return;
    }

    setLoading(true);
    try {
      await deleteDoc(doc(db, 'courses', courseId));
      
      console.log('✅ تم حذف الكورس:', courseId);
      toast.success('تم حذف الكورس بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      toast.error('فشل في حذف الكورس');
    } finally {
      setLoading(false);
    }
  };

  // تبديل حالة الكورس (مفعل/غير مفعل)
  const handleToggleCourseStatus = async (courseId, currentStatus) => {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        isActive: !currentStatus,
        updatedAt: serverTimestamp()
      });
      
      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الكورس`);
    } catch (error) {
      console.error('❌ خطأ في تغيير حالة الكورس:', error);
      toast.error('فشل في تغيير حالة الكورس');
    }
  };

  // فتح نافذة إضافة كورس جديد
  const handleOpenAddDialog = () => {
    setEditingCourse(null);
    setFormData({
      title: '',
      description: '',
      instructor: 'علاء عبد الحميد',
      duration: '',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      totalVideos: 0,
      enrolledStudents: 0,
      videos: []
    });
    setOpenDialog(true);
  };

  // فتح نافذة تعديل كورس
  const handleOpenEditDialog = (course) => {
    setEditingCourse(course);
    setFormData({
      title: course.title || '',
      description: course.description || '',
      instructor: course.instructor || 'علاء عبد الحميد',
      duration: course.duration || '',
      level: course.level || 'مبتدئ',
      price: course.price || 0,
      isActive: course.isActive !== undefined ? course.isActive : true,
      totalVideos: course.totalVideos || 0,
      enrolledStudents: course.enrolledStudents || 0,
      videos: course.videos || []
    });
    setOpenDialog(true);
  };

  // إغلاق النافذة
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingCourse(null);
    setFormData({
      title: '',
      description: '',
      instructor: 'علاء عبد الحميد',
      duration: '',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      totalVideos: 0,
      enrolledStudents: 0,
      videos: []
    });
  };

  // تحديث بيانات النموذج
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    if (date.toDate) date = date.toDate();
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <School color="primary" />
          إدارة الكورسات
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleOpenAddDialog}
          size="large"
          sx={{ borderRadius: 2 }}
        >
          إضافة كورس جديد
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="primary">{courses.length}</Typography>
            <Typography variant="body2" color="text.secondary">إجمالي الكورسات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="success.main">
              {courses.filter(c => c.isActive).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">الكورسات المفعلة</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="info.main">
              {courses.reduce((total, course) => total + (course.totalVideos || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">إجمالي الفيديوهات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="warning.main">
              {courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">إجمالي التسجيلات</Typography>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الكورسات */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة الكورسات ({courses.length})
          </Typography>
          
          {loading && (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          )}

          {!loading && courses.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا توجد كورسات حالياً. اضغط على "إضافة كورس جديد" لإنشاء أول كورس.
            </Alert>
          )}

          {!loading && courses.length > 0 && (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>عنوان الكورس</TableCell>
                    <TableCell>المدرب</TableCell>
                    <TableCell>المستوى</TableCell>
                    <TableCell>المدة</TableCell>
                    <TableCell>الفيديوهات</TableCell>
                    <TableCell>الطلاب</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>تاريخ الإنشاء</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {courses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {course.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {course.description?.substring(0, 50)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{course.instructor}</TableCell>
                      <TableCell>
                        <Chip 
                          label={course.level} 
                          size="small"
                          color={
                            course.level === 'مبتدئ' ? 'success' :
                            course.level === 'متوسط' ? 'warning' : 'error'
                          }
                        />
                      </TableCell>
                      <TableCell>{course.duration}</TableCell>
                      <TableCell>
                        <Chip 
                          icon={<PlayCircle />}
                          label={course.totalVideos || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<People />}
                          label={course.enrolledStudents || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={course.isActive}
                              onChange={() => handleToggleCourseStatus(course.id, course.isActive)}
                              size="small"
                            />
                          }
                          label={course.isActive ? 'مفعل' : 'غير مفعل'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDate(course.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <IconButton
                            size="small"
                            onClick={() => handleOpenEditDialog(course)}
                            color="primary"
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteCourse(course.id, course.title)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="info"
                          >
                            <VideoLibrary />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة/تعديل الكورس */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingCourse ? 'تعديل الكورس' : 'إضافة كورس جديد'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الكورس *"
                value={formData.title}
                onChange={(e) => handleFormChange('title', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="وصف الكورس *"
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
                variant="outlined"
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="المدرب"
                value={formData.instructor}
                onChange={(e) => handleFormChange('instructor', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="مدة الكورس"
                value={formData.duration}
                onChange={(e) => handleFormChange('duration', e.target.value)}
                variant="outlined"
                placeholder="مثال: 4 ساعات"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>المستوى</InputLabel>
                <Select
                  value={formData.level}
                  onChange={(e) => handleFormChange('level', e.target.value)}
                  label="المستوى"
                >
                  <MenuItem value="مبتدئ">مبتدئ</MenuItem>
                  <MenuItem value="متوسط">متوسط</MenuItem>
                  <MenuItem value="متقدم">متقدم</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="السعر"
                type="number"
                value={formData.price}
                onChange={(e) => handleFormChange('price', Number(e.target.value))}
                variant="outlined"
                InputProps={{
                  endAdornment: 'ريال'
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="كورس مفعل"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={editingCourse ? handleUpdateCourse : handleAddCourse}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : (editingCourse ? 'تحديث' : 'إضافة')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseManagement;
