import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Send,
  Security,
  Phone,
  Analytics,
  Refresh,
  ExpandMore,
  ExpandLess,
  CheckCircle,
  Error,
  Warning
} from '@mui/icons-material';
import toast from 'react-hot-toast';

const PasswordResetTest = () => {
  const [testPhone, setTestPhone] = useState('0506747770');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState(null);
  const [recentLogs, setRecentLogs] = useState([]);
  const [showLogs, setShowLogs] = useState(false);
  const [testResults, setTestResults] = useState([]);

  // جلب إحصائيات الأمان
  const fetchSecurityStats = async () => {
    try {
      const response = await fetch('https://us-central1-marketwise-academy-qhizq.cloudfunctions.net/getSecurityStats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
        setRecentLogs(data.recentLogs);
      }
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
    }
  };

  // اختبار إرسال SMS
  const testSMSService = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('https://us-central1-marketwise-academy-qhizq.cloudfunctions.net/testSMS', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: testPhone
        }),
      });

      const result = await response.json();
      
      const testResult = {
        timestamp: new Date().toLocaleString('ar-SA'),
        phone: testPhone,
        success: result.success,
        message: result.message,
        service: result.service,
        details: result.result
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 4)]);
      
      if (result.success) {
        toast.success('تم إرسال رسالة الاختبار بنجاح');
      } else {
        toast.error('فشل في إرسال رسالة الاختبار');
      }
      
      // تحديث الإحصائيات
      setTimeout(fetchSecurityStats, 1000);
      
    } catch (error) {
      console.error('خطأ في اختبار SMS:', error);
      toast.error('حدث خطأ في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  // اختبار استرداد كلمة المرور
  const testPasswordReset = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('https://us-central1-marketwise-academy-qhizq.cloudfunctions.net/forgotPassword', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: testPhone,
          timestamp: Date.now()
        }),
      });

      const result = await response.json();
      
      const testResult = {
        timestamp: new Date().toLocaleString('ar-SA'),
        phone: testPhone,
        success: result.success,
        message: result.message,
        type: 'Password Reset',
        mock: result.mock
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 4)]);
      
      if (result.success) {
        toast.success('تم إرسال كلمة المرور بنجاح');
      } else {
        toast.error(result.message || 'فشل في إرسال كلمة المرور');
      }
      
      // تحديث الإحصائيات
      setTimeout(fetchSecurityStats, 1000);
      
    } catch (error) {
      console.error('خطأ في اختبار استرداد كلمة المرور:', error);
      toast.error('حدث خطأ في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  // تحميل الإحصائيات عند بدء التشغيل
  useEffect(() => {
    fetchSecurityStats();
  }, []);

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'HIGH': return 'error';
      case 'MEDIUM': return 'warning';
      case 'LOW': return 'success';
      default: return 'default';
    }
  };

  const getEventTypeColor = (eventType) => {
    switch (eventType) {
      case 'PASSWORD_RESET_SENT': return 'success';
      case 'PASSWORD_RESET_FAILED': return 'error';
      case 'INVALID_PHONE_ATTEMPT': return 'error';
      case 'RATE_LIMITED': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Security color="primary" />
        اختبار نظام استرداد كلمة المرور
      </Typography>

      <Grid container spacing={3}>
        {/* قسم الاختبار */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🧪 اختبار الخدمات
              </Typography>

              <TextField
                fullWidth
                label="رقم الهاتف للاختبار"
                value={testPhone}
                onChange={(e) => setTestPhone(e.target.value)}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />

              <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                <Button
                  variant="contained"
                  onClick={testSMSService}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Send />}
                  fullWidth
                >
                  اختبار SMS
                </Button>

                <Button
                  variant="outlined"
                  onClick={testPasswordReset}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Security />}
                  fullWidth
                >
                  اختبار استرداد كلمة المرور
                </Button>
              </Box>

              {/* نتائج الاختبار */}
              {testResults.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    آخر نتائج الاختبار:
                  </Typography>
                  {testResults.map((result, index) => (
                    <Alert
                      key={index}
                      severity={result.success ? 'success' : 'error'}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">
                        <strong>{result.timestamp}</strong> - {result.message}
                        {result.service && ` (${result.service})`}
                        {result.mock && ' [وضع الاختبار]'}
                      </Typography>
                    </Alert>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* قسم الإحصائيات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  📊 إحصائيات الأمان
                </Typography>
                <IconButton onClick={fetchSecurityStats} size="small">
                  <Refresh />
                </IconButton>
              </Box>

              {stats ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 2 }}>
                      <Typography variant="h4" color="white">{stats.totalEvents}</Typography>
                      <Typography variant="body2" color="white">إجمالي الأحداث</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
                      <Typography variant="h4" color="white">{stats.severityLevels.HIGH}</Typography>
                      <Typography variant="body2" color="white">أحداث عالية الخطورة</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      أهم الأحداث:
                    </Typography>
                    {stats.topEvents.map((event, index) => (
                      <Chip
                        key={index}
                        label={`${event.event}: ${event.count}`}
                        color={getEventTypeColor(event.event)}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </Grid>
                </Grid>
              ) : (
                <CircularProgress />
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* قسم السجلات */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  📋 السجلات الأمنية الحديثة
                </Typography>
                <Button
                  onClick={() => setShowLogs(!showLogs)}
                  endIcon={showLogs ? <ExpandLess /> : <ExpandMore />}
                >
                  {showLogs ? 'إخفاء' : 'عرض'} السجلات
                </Button>
              </Box>

              <Collapse in={showLogs}>
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>الوقت</TableCell>
                        <TableCell>نوع الحدث</TableCell>
                        <TableCell>الخطورة</TableCell>
                        <TableCell>التفاصيل</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentLogs.map((log, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {new Date(log.createdAt).toLocaleString('ar-SA')}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.eventType}
                              color={getEventTypeColor(log.eventType)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.severity}
                              color={getSeverityColor(log.severity)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {log.data?.phone && `📱 ${log.data.phone}`}
                            {log.data?.error && ` ❌ ${log.data.error}`}
                            {log.data?.mock && ' 🧪'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Collapse>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* معلومات إضافية */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ℹ️ معلومات مهمة
          </Typography>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>وضع الاختبار:</strong> النظام يستخدم خدمة SMS وهمية للاختبار. 
              لتفعيل الإرسال الحقيقي، يجب إعداد Twilio في إعدادات Firebase Functions.
            </Typography>
          </Alert>

          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>رقم المدير المسجل:</strong> 0506747770 - فقط هذا الرقم يمكنه استلام كلمة المرور.
            </Typography>
          </Alert>

          <Alert severity="success">
            <Typography variant="body2">
              <strong>كلمة المرور:</strong> Admin123! - هذه هي كلمة المرور التي سيتم إرسالها.
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PasswordResetTest;
